package com.thtf.opengis.loadforecast.mapper;

import com.thtf.opengis.loadforecast.entity.LoadProportion;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 负荷百分比数据访问接口
 */
@Mapper
public interface LoadProportionMapper {

    /**
     * 插入负荷百分比记录
     */
    @Insert("INSERT INTO op_loadproportion_t (id, period, temperature, proportion, efficiency, " +
            "creator, creator_name, create_date, updator, updator_name, update_date, remark, valid) " +
            "VALUES (#{id}, #{period}, #{temperature}, #{proportion}, #{efficiency}, " +
            "#{creator}, #{creatorName}, #{createDate}, #{updator}, #{updatorName}, #{updateDate}, #{remark}, #{valid})")
    int insert(LoadProportion loadProportion);

    /**
     * 根据ID查询负荷百分比记录
     */
    @Select("SELECT * FROM op_loadproportion_t WHERE id = #{id}")
    LoadProportion selectById(Integer id);

    /**
     * 根据采暖期查询负荷百分比记录
     */
    @Select("SELECT * FROM op_loadproportion_t WHERE period = #{period} AND valid = 1 ORDER BY temperature")
    List<LoadProportion> selectByPeriod(String period);

    /**
     * 根据采暖期和温度范围查询负荷百分比记录
     */
    @Select("SELECT * FROM op_loadproportion_t WHERE period = #{period} AND temperature BETWEEN #{minTemp} AND #{maxTemp} AND valid = 1 ORDER BY temperature")
    List<LoadProportion> selectByPeriodAndTemperatureRange(@Param("period") String period, 
                                                           @Param("minTemp") Double minTemp, 
                                                           @Param("maxTemp") Double maxTemp);

    /**
     * 查询指定采暖期的温度范围
     */
    @Select("SELECT MIN(temperature) as minTemp, MAX(temperature) as maxTemp FROM op_loadproportion_t WHERE period = #{period} AND valid = 1")
    @Results({
        @Result(column = "minTemp", property = "minTemperature"),
        @Result(column = "maxTemp", property = "maxTemperature")
    })
    TemperatureRange selectTemperatureRangeByPeriod(String period);

    /**
     * 更新负荷百分比记录
     */
    @Update("UPDATE op_loadproportion_t SET period = #{period}, temperature = #{temperature}, " +
            "proportion = #{proportion}, efficiency = #{efficiency}, updator = #{updator}, " +
            "updator_name = #{updatorName}, update_date = #{updateDate}, remark = #{remark} WHERE id = #{id}")
    int update(LoadProportion loadProportion);

    /**
     * 删除负荷百分比记录（逻辑删除）
     */
    @Update("UPDATE op_loadproportion_t SET valid = 0 WHERE id = #{id}")
    int deleteById(Integer id);

    /**
     * 批量插入负荷百分比记录
     */
    @Insert("<script>" +
            "INSERT INTO op_loadproportion_t (id, period, temperature, proportion, efficiency, " +
            "creator, creator_name, create_date, updator, updator_name, update_date, remark, valid) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.period}, #{item.temperature}, #{item.proportion}, #{item.efficiency}, " +
            "#{item.creator}, #{item.creatorName}, #{item.createDate}, #{item.updator}, #{item.updatorName}, " +
            "#{item.updateDate}, #{item.remark}, #{item.valid})" +
            "</foreach>" +
            "</script>")
    int batchInsert(List<LoadProportion> loadProportions);

    /**
     * 查询所有有效的采暖期
     */
    @Select("SELECT DISTINCT period FROM op_loadproportion_t WHERE valid = 1 ORDER BY period DESC")
    List<String> selectAllPeriods();

    /**
     * 统计指定采暖期的记录数量
     */
    @Select("SELECT COUNT(*) FROM op_loadproportion_t WHERE period = #{period} AND valid = 1")
    int countByPeriod(String period);

    /**
     * 温度范围内部类
     */
    class TemperatureRange {
        private Double minTemperature;
        private Double maxTemperature;

        public Double getMinTemperature() {
            return minTemperature;
        }

        public void setMinTemperature(Double minTemperature) {
            this.minTemperature = minTemperature;
        }

        public Double getMaxTemperature() {
            return maxTemperature;
        }

        public void setMaxTemperature(Double maxTemperature) {
            this.maxTemperature = maxTemperature;
        }
    }
}
