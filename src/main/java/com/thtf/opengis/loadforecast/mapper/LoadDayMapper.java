package com.thtf.opengis.loadforecast.mapper;

import com.thtf.opengis.loadforecast.entity.LoadDay;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * 负荷预测天数据访问接口
 */
@Mapper
public interface LoadDayMapper {

    /**
     * 插入负荷预测天记录
     */
    @Insert("INSERT INTO op_loadday_t (id, period, forcast_target, target_name, forcast_date, " +
            "area, temperature, forcast_heat, real_heat, efficiency, target_id, real_efficiency) " +
            "VALUES (#{id}, #{period}, #{forecastTarget}, #{targetName}, #{forecastDate}, " +
            "#{area}, #{temperature}, #{forecastHeat}, #{realHeat}, #{efficiency}, #{targetId}, #{realEfficiency})")
    int insert(LoadDay loadDay);

    /**
     * 根据ID查询负荷预测天记录
     */
    @Select("SELECT * FROM op_loadday_t WHERE id = #{id}")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_name", property = "targetName"),
        @Result(column = "forcast_date", property = "forecastDate"),
        @Result(column = "forcast_heat", property = "forecastHeat"),
        @Result(column = "real_heat", property = "realHeat"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "real_efficiency", property = "realEfficiency")
    })
    LoadDay selectById(Long id);

    /**
     * 根据采暖期查询负荷预测天记录
     */
    @Select("SELECT * FROM op_loadday_t WHERE period = #{period} ORDER BY forcast_date DESC")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_name", property = "targetName"),
        @Result(column = "forcast_date", property = "forecastDate"),
        @Result(column = "forcast_heat", property = "forecastHeat"),
        @Result(column = "real_heat", property = "realHeat"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "real_efficiency", property = "realEfficiency")
    })
    List<LoadDay> selectByPeriod(String period);

    /**
     * 根据采暖期和预测对象查询记录
     */
    @Select("SELECT * FROM op_loadday_t WHERE period = #{period} AND forcast_target = #{forecastTarget} " +
            "ORDER BY forcast_date DESC")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_name", property = "targetName"),
        @Result(column = "forcast_date", property = "forecastDate"),
        @Result(column = "forcast_heat", property = "forecastHeat"),
        @Result(column = "real_heat", property = "realHeat"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "real_efficiency", property = "realEfficiency")
    })
    List<LoadDay> selectByPeriodAndTarget(@Param("period") String period, 
                                         @Param("forecastTarget") Integer forecastTarget);

    /**
     * 根据采暖期、预测对象和目标ID查询记录
     */
    @Select("SELECT * FROM op_loadday_t WHERE period = #{period} AND forcast_target = #{forecastTarget} " +
            "AND target_id = #{targetId} ORDER BY forcast_date DESC")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_name", property = "targetName"),
        @Result(column = "forcast_date", property = "forecastDate"),
        @Result(column = "forcast_heat", property = "forecastHeat"),
        @Result(column = "real_heat", property = "realHeat"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "real_efficiency", property = "realEfficiency")
    })
    List<LoadDay> selectByPeriodTargetAndId(@Param("period") String period, 
                                           @Param("forecastTarget") Integer forecastTarget,
                                           @Param("targetId") Long targetId);

    /**
     * 根据日期范围查询记录
     */
    @Select("SELECT * FROM op_loadday_t WHERE period = #{period} AND forcast_target = #{forecastTarget} " +
            "AND target_id = #{targetId} AND forcast_date BETWEEN #{startDate} AND #{endDate} " +
            "ORDER BY forcast_date")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_name", property = "targetName"),
        @Result(column = "forcast_date", property = "forecastDate"),
        @Result(column = "forcast_heat", property = "forecastHeat"),
        @Result(column = "real_heat", property = "realHeat"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "real_efficiency", property = "realEfficiency")
    })
    List<LoadDay> selectByDateRange(@Param("period") String period, 
                                   @Param("forecastTarget") Integer forecastTarget,
                                   @Param("targetId") Long targetId,
                                   @Param("startDate") Date startDate,
                                   @Param("endDate") Date endDate);

    /**
     * 查询当年数据用于K2系数计算
     */
    @Select("SELECT * FROM op_loadday_t WHERE period = #{period} AND forcast_target = #{forecastTarget} " +
            "AND target_id = #{targetId} AND YEAR(forcast_date) = YEAR(#{currentDate}) " +
            "AND real_heat IS NOT NULL AND forcast_heat IS NOT NULL AND forcast_heat > 0 " +
            "ORDER BY forcast_date")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_name", property = "targetName"),
        @Result(column = "forcast_date", property = "forecastDate"),
        @Result(column = "forcast_heat", property = "forecastHeat"),
        @Result(column = "real_heat", property = "realHeat"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "real_efficiency", property = "realEfficiency")
    })
    List<LoadDay> selectCurrentYearDataForK2(@Param("period") String period, 
                                            @Param("forecastTarget") Integer forecastTarget,
                                            @Param("targetId") Long targetId,
                                            @Param("currentDate") Date currentDate);

    /**
     * 查询历史数据用于K1系数计算
     */
    @Select("SELECT * FROM op_loadday_t WHERE period = #{period} AND forcast_target = #{forecastTarget} " +
            "AND target_id = #{targetId} AND temperature BETWEEN #{minTemp} AND #{maxTemp} " +
            "AND real_efficiency IS NOT NULL AND efficiency IS NOT NULL AND efficiency > 0 " +
            "ORDER BY forcast_date")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_name", property = "targetName"),
        @Result(column = "forcast_date", property = "forecastDate"),
        @Result(column = "forcast_heat", property = "forecastHeat"),
        @Result(column = "real_heat", property = "realHeat"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "real_efficiency", property = "realEfficiency")
    })
    List<LoadDay> selectHistoricalDataForK1(@Param("period") String period, 
                                           @Param("forecastTarget") Integer forecastTarget,
                                           @Param("targetId") Long targetId,
                                           @Param("minTemp") Double minTemp,
                                           @Param("maxTemp") Double maxTemp);

    /**
     * 更新负荷预测天记录
     */
    @Update("UPDATE op_loadday_t SET period = #{period}, forcast_target = #{forecastTarget}, " +
            "target_name = #{targetName}, forcast_date = #{forecastDate}, area = #{area}, " +
            "temperature = #{temperature}, forcast_heat = #{forecastHeat}, real_heat = #{realHeat}, " +
            "efficiency = #{efficiency}, target_id = #{targetId}, real_efficiency = #{realEfficiency} WHERE id = #{id}")
    int update(LoadDay loadDay);

    /**
     * 更新实际热量和实际能耗
     */
    @Update("UPDATE op_loadday_t SET real_heat = #{realHeat}, real_efficiency = #{realEfficiency} " +
            "WHERE id = #{id}")
    int updateRealData(@Param("id") Long id, 
                      @Param("realHeat") Double realHeat, 
                      @Param("realEfficiency") Double realEfficiency);

    /**
     * 删除负荷预测天记录
     */
    @Delete("DELETE FROM op_loadday_t WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 批量插入负荷预测天记录
     */
    @Insert("<script>" +
            "INSERT INTO op_loadday_t (id, period, forcast_target, target_name, forcast_date, " +
            "area, temperature, forcast_heat, real_heat, efficiency, target_id, real_efficiency) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.period}, #{item.forecastTarget}, #{item.targetName}, #{item.forecastDate}, " +
            "#{item.area}, #{item.temperature}, #{item.forecastHeat}, #{item.realHeat}, " +
            "#{item.efficiency}, #{item.targetId}, #{item.realEfficiency})" +
            "</foreach>" +
            "</script>")
    int batchInsert(List<LoadDay> loadDays);

    /**
     * 查询所有采暖期
     */
    @Select("SELECT DISTINCT period FROM op_loadday_t ORDER BY period DESC")
    List<String> selectAllPeriods();

    /**
     * 统计指定采暖期的记录数量
     */
    @Select("SELECT COUNT(*) FROM op_loadday_t WHERE period = #{period}")
    int countByPeriod(String period);

    /**
     * 查询最新的预测记录
     */
    @Select("SELECT * FROM op_loadday_t WHERE period = #{period} AND forcast_target = #{forecastTarget} " +
            "AND target_id = #{targetId} ORDER BY forcast_date DESC LIMIT 1")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_name", property = "targetName"),
        @Result(column = "forcast_date", property = "forecastDate"),
        @Result(column = "forcast_heat", property = "forecastHeat"),
        @Result(column = "real_heat", property = "realHeat"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "real_efficiency", property = "realEfficiency")
    })
    LoadDay selectLatest(@Param("period") String period, 
                        @Param("forecastTarget") Integer forecastTarget,
                        @Param("targetId") Long targetId);
}
