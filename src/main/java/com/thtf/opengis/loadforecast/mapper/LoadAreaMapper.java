package com.thtf.opengis.loadforecast.mapper;

import com.thtf.opengis.loadforecast.entity.LoadArea;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * 预测面积数据访问接口
 */
@Mapper
public interface LoadAreaMapper {

    /**
     * 插入预测面积记录
     */
    @Insert("INSERT INTO op_loadarea_t (id, period, forcast_target, target_id, area_date, " +
            "building_type, insulated, area_normal, area_stop, stop_proportion, area_adjust, area) " +
            "VALUES (#{id}, #{period}, #{forecastTarget}, #{targetId}, #{areaDate}, " +
            "#{buildingType}, #{insulated}, #{areaNormal}, #{areaStop}, #{stopProportion}, #{areaAdjust}, #{area})")
    int insert(LoadArea loadArea);

    /**
     * 根据ID查询预测面积记录
     */
    @Select("SELECT * FROM op_loadarea_t WHERE id = #{id}")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "area_date", property = "areaDate"),
        @Result(column = "building_type", property = "buildingType"),
        @Result(column = "area_normal", property = "areaNormal"),
        @Result(column = "area_stop", property = "areaStop"),
        @Result(column = "stop_proportion", property = "stopProportion"),
        @Result(column = "area_adjust", property = "areaAdjust")
    })
    LoadArea selectById(Long id);

    /**
     * 根据采暖期查询预测面积记录
     */
    @Select("SELECT * FROM op_loadarea_t WHERE period = #{period} ORDER BY forcast_target, target_id, area_date")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "area_date", property = "areaDate"),
        @Result(column = "building_type", property = "buildingType"),
        @Result(column = "area_normal", property = "areaNormal"),
        @Result(column = "area_stop", property = "areaStop"),
        @Result(column = "stop_proportion", property = "stopProportion"),
        @Result(column = "area_adjust", property = "areaAdjust")
    })
    List<LoadArea> selectByPeriod(String period);

    /**
     * 根据采暖期和预测对象查询面积记录
     */
    @Select("SELECT * FROM op_loadarea_t WHERE period = #{period} AND forcast_target = #{forecastTarget} " +
            "ORDER BY target_id, area_date")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "area_date", property = "areaDate"),
        @Result(column = "building_type", property = "buildingType"),
        @Result(column = "area_normal", property = "areaNormal"),
        @Result(column = "area_stop", property = "areaStop"),
        @Result(column = "stop_proportion", property = "stopProportion"),
        @Result(column = "area_adjust", property = "areaAdjust")
    })
    List<LoadArea> selectByPeriodAndTarget(@Param("period") String period, 
                                          @Param("forecastTarget") Integer forecastTarget);

    /**
     * 根据采暖期、预测对象和目标ID查询面积记录
     */
    @Select("SELECT * FROM op_loadarea_t WHERE period = #{period} AND forcast_target = #{forecastTarget} " +
            "AND target_id = #{targetId} ORDER BY area_date")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "area_date", property = "areaDate"),
        @Result(column = "building_type", property = "buildingType"),
        @Result(column = "area_normal", property = "areaNormal"),
        @Result(column = "area_stop", property = "areaStop"),
        @Result(column = "stop_proportion", property = "stopProportion"),
        @Result(column = "area_adjust", property = "areaAdjust")
    })
    List<LoadArea> selectByPeriodTargetAndId(@Param("period") String period, 
                                            @Param("forecastTarget") Integer forecastTarget,
                                            @Param("targetId") Long targetId);

    /**
     * 根据日期查询最新的面积记录
     */
    @Select("SELECT * FROM op_loadarea_t WHERE period = #{period} AND forcast_target = #{forecastTarget} " +
            "AND target_id = #{targetId} AND area_date <= #{date} ORDER BY area_date DESC LIMIT 1")
    @Results({
        @Result(column = "forcast_target", property = "forecastTarget"),
        @Result(column = "target_id", property = "targetId"),
        @Result(column = "area_date", property = "areaDate"),
        @Result(column = "building_type", property = "buildingType"),
        @Result(column = "area_normal", property = "areaNormal"),
        @Result(column = "area_stop", property = "areaStop"),
        @Result(column = "stop_proportion", property = "stopProportion"),
        @Result(column = "area_adjust", property = "areaAdjust")
    })
    LoadArea selectLatestByDate(@Param("period") String period, 
                               @Param("forecastTarget") Integer forecastTarget,
                               @Param("targetId") Long targetId,
                               @Param("date") Date date);

    /**
     * 更新预测面积记录
     */
    @Update("UPDATE op_loadarea_t SET period = #{period}, forcast_target = #{forecastTarget}, " +
            "target_id = #{targetId}, area_date = #{areaDate}, building_type = #{buildingType}, " +
            "insulated = #{insulated}, area_normal = #{areaNormal}, area_stop = #{areaStop}, " +
            "stop_proportion = #{stopProportion}, area_adjust = #{areaAdjust}, area = #{area} WHERE id = #{id}")
    int update(LoadArea loadArea);

    /**
     * 删除预测面积记录
     */
    @Delete("DELETE FROM op_loadarea_t WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 批量插入预测面积记录
     */
    @Insert("<script>" +
            "INSERT INTO op_loadarea_t (id, period, forcast_target, target_id, area_date, " +
            "building_type, insulated, area_normal, area_stop, stop_proportion, area_adjust, area) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.period}, #{item.forecastTarget}, #{item.targetId}, #{item.areaDate}, " +
            "#{item.buildingType}, #{item.insulated}, #{item.areaNormal}, #{item.areaStop}, " +
            "#{item.stopProportion}, #{item.areaAdjust}, #{item.area})" +
            "</foreach>" +
            "</script>")
    int batchInsert(List<LoadArea> loadAreas);

    /**
     * 统计指定采暖期的总面积
     */
    @Select("SELECT SUM(area) FROM op_loadarea_t WHERE period = #{period}")
    Double sumAreaByPeriod(String period);

    /**
     * 统计指定采暖期和预测对象的总面积
     */
    @Select("SELECT SUM(area) FROM op_loadarea_t WHERE period = #{period} AND forcast_target = #{forecastTarget}")
    Double sumAreaByPeriodAndTarget(@Param("period") String period, 
                                   @Param("forecastTarget") Integer forecastTarget);

    /**
     * 查询所有采暖期
     */
    @Select("SELECT DISTINCT period FROM op_loadarea_t ORDER BY period DESC")
    List<String> selectAllPeriods();

    /**
     * 查询指定采暖期的所有预测对象
     */
    @Select("SELECT DISTINCT forcast_target FROM op_loadarea_t WHERE period = #{period} ORDER BY forcast_target")
    List<Integer> selectForecastTargetsByPeriod(String period);
}
