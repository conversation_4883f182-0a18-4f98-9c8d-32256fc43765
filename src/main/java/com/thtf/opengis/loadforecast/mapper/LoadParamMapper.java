package com.thtf.opengis.loadforecast.mapper;

import com.thtf.opengis.loadforecast.entity.LoadParam;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 预测参数数据访问接口
 */
@Mapper
public interface LoadParamMapper {

    /**
     * 插入预测参数记录
     */
    @Insert("INSERT INTO op_loadparam_t (period, building_type, insulated, efficiency) " +
            "VALUES (#{period}, #{buildingType}, #{insulated}, #{efficiency})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LoadParam loadParam);

    /**
     * 根据ID查询预测参数记录
     */
    @Select("SELECT * FROM op_loadparam_t WHERE id = #{id}")
    LoadParam selectById(Long id);

    /**
     * 根据采暖期查询预测参数记录
     */
    @Select("SELECT * FROM op_loadparam_t WHERE period = #{period} ORDER BY building_type, insulated")
    List<LoadParam> selectByPeriod(String period);

    /**
     * 根据采暖期、建筑类型和节能性查询预测参数
     */
    @Select("SELECT * FROM op_loadparam_t WHERE period = #{period} AND building_type = #{buildingType} AND insulated = #{insulated}")
    LoadParam selectByPeriodAndType(@Param("period") String period, 
                                   @Param("buildingType") Integer buildingType, 
                                   @Param("insulated") Integer insulated);

    /**
     * 查询节能建筑参数
     */
    @Select("SELECT * FROM op_loadparam_t WHERE period = #{period} AND insulated = 1 ORDER BY building_type")
    List<LoadParam> selectEnergyEfficientByPeriod(String period);

    /**
     * 查询非节能建筑参数
     */
    @Select("SELECT * FROM op_loadparam_t WHERE period = #{period} AND insulated = 2 ORDER BY building_type")
    List<LoadParam> selectNonEnergyEfficientByPeriod(String period);

    /**
     * 更新预测参数记录
     */
    @Update("UPDATE op_loadparam_t SET period = #{period}, building_type = #{buildingType}, " +
            "insulated = #{insulated}, efficiency = #{efficiency} WHERE id = #{id}")
    int update(LoadParam loadParam);

    /**
     * 删除预测参数记录
     */
    @Delete("DELETE FROM op_loadparam_t WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 批量插入预测参数记录
     */
    @Insert("<script>" +
            "INSERT INTO op_loadparam_t (period, building_type, insulated, efficiency) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.period}, #{item.buildingType}, #{item.insulated}, #{item.efficiency})" +
            "</foreach>" +
            "</script>")
    int batchInsert(List<LoadParam> loadParams);

    /**
     * 查询所有采暖期
     */
    @Select("SELECT DISTINCT period FROM op_loadparam_t ORDER BY period DESC")
    List<String> selectAllPeriods();

    /**
     * 统计指定采暖期的参数数量
     */
    @Select("SELECT COUNT(*) FROM op_loadparam_t WHERE period = #{period}")
    int countByPeriod(String period);

    /**
     * 根据采暖期删除所有参数
     */
    @Delete("DELETE FROM op_loadparam_t WHERE period = #{period}")
    int deleteByPeriod(String period);

    /**
     * 查询指定采暖期的建筑类型统计
     */
    @Select("SELECT building_type, insulated, COUNT(*) as count FROM op_loadparam_t " +
            "WHERE period = #{period} GROUP BY building_type, insulated ORDER BY building_type, insulated")
    @Results({
        @Result(column = "building_type", property = "buildingType"),
        @Result(column = "insulated", property = "insulated"),
        @Result(column = "count", property = "count")
    })
    List<BuildingTypeCount> selectBuildingTypeCountByPeriod(String period);

    /**
     * 建筑类型统计内部类
     */
    class BuildingTypeCount {
        private Integer buildingType;
        private Integer insulated;
        private Integer count;

        public Integer getBuildingType() {
            return buildingType;
        }

        public void setBuildingType(Integer buildingType) {
            this.buildingType = buildingType;
        }

        public Integer getInsulated() {
            return insulated;
        }

        public void setInsulated(Integer insulated) {
            this.insulated = insulated;
        }

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }
    }
}
