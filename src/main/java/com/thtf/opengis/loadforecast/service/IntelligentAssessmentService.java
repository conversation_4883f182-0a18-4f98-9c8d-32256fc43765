package com.thtf.opengis.loadforecast.service;

import com.thtf.opengis.loadforecast.entity.LoadDay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 智能评估服务
 * 实现系统运行状态评估和三维评价体系
 */
@Service
public class IntelligentAssessmentService {

    @Autowired
    private AlgorithmCalculationService algorithmService;

    // 评估阈值常量
    private static final double SIMILARITY_THRESHOLD = 0.05; // 5%的相似度阈值
    private static final double ACCURACY_THRESHOLD = 0.9; // 90%的准确率阈值

    /**
     * 系统运行状态枚举
     */
    public enum SystemStatus {
        NORMAL("系统正常", "维持现有策略"),
        ENERGY_EXCEEDED("能耗超标", "触发警报，排查问题"),
        OPTIMAL("系统最优", "保持当前工况"),
        MODEL_CORRECTION("模型修正", "调整K2系数");

        private final String description;
        private final String recommendation;

        SystemStatus(String description, String recommendation) {
            this.description = description;
            this.recommendation = recommendation;
        }

        public String getDescription() {
            return description;
        }

        public String getRecommendation() {
            return recommendation;
        }
    }

    /**
     * 系统评估结果
     */
    public static class AssessmentResult {
        private SystemStatus status;
        private String description;
        private String recommendation;
        private Map<String, Double> indicators;
        private List<String> alerts;

        public AssessmentResult(SystemStatus status) {
            this.status = status;
            this.description = status.getDescription();
            this.recommendation = status.getRecommendation();
            this.indicators = new HashMap<>();
            this.alerts = new ArrayList<>();
        }

        // Getters and Setters
        public SystemStatus getStatus() { return status; }
        public void setStatus(SystemStatus status) { this.status = status; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getRecommendation() { return recommendation; }
        public void setRecommendation(String recommendation) { this.recommendation = recommendation; }
        
        public Map<String, Double> getIndicators() { return indicators; }
        public void setIndicators(Map<String, Double> indicators) { this.indicators = indicators; }
        
        public List<String> getAlerts() { return alerts; }
        public void setAlerts(List<String> alerts) { this.alerts = alerts; }
        
        public void addIndicator(String name, Double value) {
            this.indicators.put(name, value);
        }
        
        public void addAlert(String alert) {
            this.alerts.add(alert);
        }
    }

    /**
     * 执行系统评估
     */
    public AssessmentResult performSystemAssessment(LoadDay loadDay) {
        // 计算三种指标
        ThreeIndicators indicators = calculateThreeIndicators(loadDay);
        
        // 评估系统状态
        SystemStatus status = evaluateSystemStatus(indicators);
        
        // 创建评估结果
        AssessmentResult result = new AssessmentResult(status);
        result.addIndicator("benchmarkIndicator", indicators.getBenchmarkIndicator());
        result.addIndicator("forecastIndicator", indicators.getForecastIndicator());
        result.addIndicator("actualIndicator", indicators.getActualIndicator());
        
        // 添加详细分析
        addDetailedAnalysis(result, indicators);
        
        return result;
    }

    /**
     * 批量评估
     */
    public List<AssessmentResult> performBatchAssessment(List<LoadDay> loadDays) {
        List<AssessmentResult> results = new ArrayList<>();
        
        for (LoadDay loadDay : loadDays) {
            if (loadDay.getRealHeat() != null && loadDay.getRealEfficiency() != null) {
                AssessmentResult result = performSystemAssessment(loadDay);
                results.add(result);
            }
        }
        
        return results;
    }

    /**
     * 计算三维指标
     */
    private ThreeIndicators calculateThreeIndicators(LoadDay loadDay) {
        // 1. 标杆指标 - 理论最优值
        Double benchmarkIndicator = calculateBenchmarkIndicator(loadDay);
        
        // 2. 预测指标 - 算法预测值
        Double forecastIndicator = loadDay.getEfficiency();
        
        // 3. 实际指标 - 实际运行值
        Double actualIndicator = loadDay.getRealEfficiency();
        
        return new ThreeIndicators(benchmarkIndicator, forecastIndicator, actualIndicator);
    }

    /**
     * 计算标杆指标
     */
    private Double calculateBenchmarkIndicator(LoadDay loadDay) {
        try {
            // 获取采暖季阶段信息
            String heatingStage = algorithmService.determineHeatingStage(loadDay.getForecastDate());
            Map<String, Double> stageTemperatures = algorithmService.getStageTemperatures(heatingStage);
            Double designTemperature = stageTemperatures.get("design");
            
            // 计算标杆热指标
            return algorithmService.calculateBenchmarkHeatIndex(
                    loadDay.getPeriod(), 
                    loadDay.getTemperature(), 
                    designTemperature,
                    loadDay.getForecastTarget(), 
                    loadDay.getTargetId()
            );
        } catch (Exception e) {
            // 如果计算失败，返回预测指标作为参考
            return loadDay.getEfficiency();
        }
    }

    /**
     * 评估系统状态
     */
    private SystemStatus evaluateSystemStatus(ThreeIndicators indicators) {
        Double benchmark = indicators.getBenchmarkIndicator();
        Double forecast = indicators.getForecastIndicator();
        Double actual = indicators.getActualIndicator();
        
        if (benchmark == null || forecast == null || actual == null) {
            return SystemStatus.MODEL_CORRECTION;
        }
        
        // 计算相似度
        boolean benchmarkForecastSimilar = isSimilar(benchmark, forecast);
        boolean benchmarkActualSimilar = isSimilar(benchmark, actual);
        boolean forecastActualSimilar = isSimilar(forecast, actual);
        
        // 评估规则
        if (benchmarkForecastSimilar && benchmarkActualSimilar && forecastActualSimilar) {
            return SystemStatus.NORMAL; // 预测指标≈标杆指标≈实际指标
        } else if (benchmark < forecast && forecastActualSimilar) {
            return SystemStatus.ENERGY_EXCEEDED; // 标杆指标<预测指标≈实际指标
        } else if (benchmark > forecast && forecastActualSimilar) {
            return SystemStatus.OPTIMAL; // 标杆指标>预测指标≈实际指标
        } else if (!forecastActualSimilar) {
            return SystemStatus.MODEL_CORRECTION; // 预测指标≠实际指标
        } else {
            return SystemStatus.NORMAL; // 默认正常状态
        }
    }

    /**
     * 判断两个值是否相似
     */
    private boolean isSimilar(Double value1, Double value2) {
        if (value1 == null || value2 == null) {
            return false;
        }
        
        double larger = Math.max(Math.abs(value1), Math.abs(value2));
        if (larger == 0) {
            return true;
        }
        
        double difference = Math.abs(value1 - value2);
        return (difference / larger) <= SIMILARITY_THRESHOLD;
    }

    /**
     * 添加详细分析
     */
    private void addDetailedAnalysis(AssessmentResult result, ThreeIndicators indicators) {
        Double benchmark = indicators.getBenchmarkIndicator();
        Double forecast = indicators.getForecastIndicator();
        Double actual = indicators.getActualIndicator();
        
        if (benchmark != null && forecast != null) {
            double benchmarkForecastDiff = Math.abs(benchmark - forecast) / Math.max(benchmark, forecast) * 100;
            result.addIndicator("benchmarkForecastDifference", benchmarkForecastDiff);
            
            if (benchmarkForecastDiff > 10) {
                result.addAlert("标杆指标与预测指标差异较大: " + String.format("%.2f%%", benchmarkForecastDiff));
            }
        }
        
        if (forecast != null && actual != null) {
            double forecastActualDiff = Math.abs(forecast - actual) / Math.max(forecast, actual) * 100;
            result.addIndicator("forecastActualDifference", forecastActualDiff);
            
            if (forecastActualDiff > 10) {
                result.addAlert("预测指标与实际指标差异较大: " + String.format("%.2f%%", forecastActualDiff));
            }
        }
        
        if (benchmark != null && actual != null) {
            double benchmarkActualDiff = Math.abs(benchmark - actual) / Math.max(benchmark, actual) * 100;
            result.addIndicator("benchmarkActualDifference", benchmarkActualDiff);
            
            if (benchmarkActualDiff > 15) {
                result.addAlert("标杆指标与实际指标差异过大: " + String.format("%.2f%%", benchmarkActualDiff));
            }
        }
        
        // 能耗评估
        if (actual != null && benchmark != null) {
            if (actual > benchmark * 1.2) {
                result.addAlert("实际能耗超出标杆值20%以上，建议检查系统运行状态");
            } else if (actual < benchmark * 0.8) {
                result.addAlert("实际能耗低于标杆值20%以上，系统运行良好");
            }
        }
    }

    /**
     * 生成优化建议
     */
    public List<String> generateOptimizationSuggestions(AssessmentResult result) {
        List<String> suggestions = new ArrayList<>();
        
        switch (result.getStatus()) {
            case NORMAL:
                suggestions.add("系统运行正常，继续保持当前运行策略");
                suggestions.add("定期监控三维指标变化趋势");
                break;
                
            case ENERGY_EXCEEDED:
                suggestions.add("立即检查供热设备运行状态");
                suggestions.add("排查管网是否存在泄漏或堵塞");
                suggestions.add("检查换热站设备效率");
                suggestions.add("分析用户用热行为是否异常");
                break;
                
            case OPTIMAL:
                suggestions.add("当前运行状态良好，保持现有工况");
                suggestions.add("总结优化经验，推广到其他区域");
                suggestions.add("适当降低供热参数，进一步节能");
                break;
                
            case MODEL_CORRECTION:
                suggestions.add("更新预测模型参数");
                suggestions.add("增加历史数据样本量");
                suggestions.add("检查实时数据采集准确性");
                suggestions.add("调整修正系数K2");
                break;
        }
        
        // 根据具体指标添加建议
        Double forecastActualDiff = result.getIndicators().get("forecastActualDifference");
        if (forecastActualDiff != null && forecastActualDiff > 20) {
            suggestions.add("预测偏差过大，建议重新校准预测模型");
        }
        
        return suggestions;
    }

    /**
     * 三维指标内部类
     */
    private static class ThreeIndicators {
        private Double benchmarkIndicator;
        private Double forecastIndicator;
        private Double actualIndicator;

        public ThreeIndicators(Double benchmarkIndicator, Double forecastIndicator, Double actualIndicator) {
            this.benchmarkIndicator = benchmarkIndicator;
            this.forecastIndicator = forecastIndicator;
            this.actualIndicator = actualIndicator;
        }

        public Double getBenchmarkIndicator() { return benchmarkIndicator; }
        public Double getForecastIndicator() { return forecastIndicator; }
        public Double getActualIndicator() { return actualIndicator; }
    }
}
