package com.thtf.opengis.loadforecast.service;

import com.thtf.opengis.loadforecast.entity.*;
import com.thtf.opengis.loadforecast.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 负荷预测主服务
 * 整合各个算法模块，提供完整的负荷预测功能
 */
@Service
public class LoadForecastService {

    @Autowired
    private AlgorithmCalculationService algorithmService;
    
    @Autowired
    private DataGovernanceService dataGovernanceService;
    
    @Autowired
    private IntelligentAssessmentService assessmentService;
    
    @Autowired
    private LoadDayMapper loadDayMapper;
    
    @Autowired
    private LoadAreaMapper loadAreaMapper;
    
    @Autowired
    private LoadParamMapper loadParamMapper;

    /**
     * 执行负荷预测
     */
    @Transactional
    public LoadDay performLoadForecast(String period, Integer forecastTarget, Long targetId, 
                                     String targetName, Date forecastDate, Double temperature, 
                                     String weatherCondition) {
        try {
            // 1. 数据清洗和验证
            dataGovernanceService.validateInputData(period, forecastTarget, targetId, forecastDate, temperature);
            
            // 2. 判断采暖季阶段
            String heatingStage = algorithmService.determineHeatingStage(forecastDate);
            Map<String, Double> stageTemperatures = algorithmService.getStageTemperatures(heatingStage);
            Double designTemperature = stageTemperatures.get("design");
            Double baseTemperature = stageTemperatures.get("base");
            
            // 3. 获取实际供热面积
            LoadArea latestArea = loadAreaMapper.selectLatestByDate(period, forecastTarget, targetId, forecastDate);
            if (latestArea == null) {
                throw new RuntimeException("未找到面积配置数据");
            }
            Double actualHeatingArea = latestArea.getActualHeatingArea();
            
            // 4. 计算标杆热指标
            Double benchmarkHeatIndex = algorithmService.calculateBenchmarkHeatIndex(
                    period, temperature, designTemperature, forecastTarget, targetId);
            
            // 5. 计算修正系数K1（历史修正系数）
            Double k1 = calculateK1ForTemperature(period, forecastTarget, targetId, temperature, 
                                                 designTemperature, baseTemperature);
            
            // 6. 计算修正系数K2（实时修正系数）
            Double k2 = algorithmService.calculateRealtimeCorrectionK2(period, forecastTarget, targetId, forecastDate);
            
            // 7. 计算预测热指标
            Double forecastHeatIndex = algorithmService.calculateForecastHeatIndex(benchmarkHeatIndex, k1, k2);
            
            // 8. 获取修正系数
            Double f1 = 1.0; // 长输管网热损修正系数，默认1.0
            Double f2 = algorithmService.getWeatherCorrectionF2(weatherCondition);
            
            // 9. 计算预测热负荷
            Double forecastHeat = algorithmService.calculateHeatLoad(forecastHeatIndex, actualHeatingArea, f1, f2);
            
            // 10. 创建预测记录
            LoadDay loadDay = new LoadDay(period, forecastTarget, targetName, forecastDate, 
                                        actualHeatingArea, temperature, targetId);
            loadDay.setForecastHeat(forecastHeat);
            loadDay.setEfficiency(forecastHeatIndex);
            
            // 11. 生成ID并保存
            loadDay.setId(generateLoadDayId());
            loadDayMapper.insert(loadDay);
            
            return loadDay;
            
        } catch (Exception e) {
            throw new RuntimeException("负荷预测失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量预测
     */
    @Transactional
    public List<LoadDay> performBatchForecast(String period, Integer forecastTarget, Long targetId, 
                                            String targetName, List<ForecastRequest> requests) {
        List<LoadDay> results = new ArrayList<>();
        
        for (ForecastRequest request : requests) {
            LoadDay result = performLoadForecast(period, forecastTarget, targetId, targetName,
                    request.getForecastDate(), request.getTemperature(), request.getWeatherCondition());
            results.add(result);
        }
        
        return results;
    }

    /**
     * 更新实际数据
     */
    @Transactional
    public void updateRealData(Long loadDayId, Double realHeat, Double realEfficiency) {
        loadDayMapper.updateRealData(loadDayId, realHeat, realEfficiency);
        
        // 触发智能评估
        LoadDay loadDay = loadDayMapper.selectById(loadDayId);
        if (loadDay != null) {
            assessmentService.performSystemAssessment(loadDay);
        }
    }

    /**
     * 计算指定温度的K1系数
     */
    private Double calculateK1ForTemperature(String period, Integer forecastTarget, Long targetId, 
                                           Double temperature, Double designTemperature, Double baseTemperature) {
        // 确定温度所在区间
        Double minTemp = Math.floor(temperature);
        Double maxTemp = minTemp + 1.0;
        
        // 计算该区间的K1系数
        return algorithmService.calculateHistoricalCorrectionK1(
                period, forecastTarget, targetId, minTemp, maxTemp, designTemperature, baseTemperature);
    }

    /**
     * 获取预测历史
     */
    public List<LoadDay> getForecastHistory(String period, Integer forecastTarget, Long targetId, 
                                          Date startDate, Date endDate) {
        return loadDayMapper.selectByDateRange(period, forecastTarget, targetId, startDate, endDate);
    }

    /**
     * 获取预测准确率统计
     */
    public Map<String, Object> getForecastAccuracyStats(String period, Integer forecastTarget, Long targetId) {
        List<LoadDay> data = loadDayMapper.selectByPeriodTargetAndId(period, forecastTarget, targetId);
        
        List<Double> accuracies = new ArrayList<>();
        double totalDeviation = 0.0;
        int validCount = 0;
        
        for (LoadDay day : data) {
            Double accuracy = day.getAccuracy();
            if (accuracy != null) {
                accuracies.add(accuracy);
                if (day.getRealHeat() != null && day.getForecastHeat() != null) {
                    totalDeviation += Math.abs(day.getRealHeat() - day.getForecastHeat());
                    validCount++;
                }
            }
        }
        
        Map<String, Object> stats = new HashMap<>();
        if (!accuracies.isEmpty()) {
            stats.put("averageAccuracy", accuracies.stream().mapToDouble(Double::doubleValue).average().orElse(0.0));
            stats.put("maxAccuracy", accuracies.stream().mapToDouble(Double::doubleValue).max().orElse(0.0));
            stats.put("minAccuracy", accuracies.stream().mapToDouble(Double::doubleValue).min().orElse(0.0));
        }
        
        if (validCount > 0) {
            stats.put("averageDeviation", totalDeviation / validCount);
        }
        
        stats.put("totalRecords", data.size());
        stats.put("validRecords", validCount);
        
        return stats;
    }

    /**
     * 生成LoadDay ID
     */
    private Long generateLoadDayId() {
        return System.currentTimeMillis();
    }

    /**
     * 预测请求内部类
     */
    public static class ForecastRequest {
        private Date forecastDate;
        private Double temperature;
        private String weatherCondition;

        public ForecastRequest() {}

        public ForecastRequest(Date forecastDate, Double temperature, String weatherCondition) {
            this.forecastDate = forecastDate;
            this.temperature = temperature;
            this.weatherCondition = weatherCondition;
        }

        public Date getForecastDate() {
            return forecastDate;
        }

        public void setForecastDate(Date forecastDate) {
            this.forecastDate = forecastDate;
        }

        public Double getTemperature() {
            return temperature;
        }

        public void setTemperature(Double temperature) {
            this.temperature = temperature;
        }

        public String getWeatherCondition() {
            return weatherCondition;
        }

        public void setWeatherCondition(String weatherCondition) {
            this.weatherCondition = weatherCondition;
        }
    }
}
