package com.thtf.opengis.loadforecast.service;

import com.thtf.opengis.loadforecast.entity.*;
import com.thtf.opengis.loadforecast.mapper.LoadDayMapper;
import com.thtf.opengis.loadforecast.mapper.LoadParamMapper;
import com.thtf.opengis.loadforecast.mapper.LoadAreaMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 算法计算服务
 * 实现供热负荷预测的核心算法
 */
@Service
public class AlgorithmCalculationService {

    @Autowired
    private LoadDayMapper loadDayMapper;
    
    @Autowired
    private LoadParamMapper loadParamMapper;
    
    @Autowired
    private LoadAreaMapper loadAreaMapper;

    /**
     * 计算标杆热指标
     * 公式：标杆热指标 = (W1 × M1 + W2 × M2) × (18 - T实际) / (18 - T设计)
     */
    public Double calculateBenchmarkHeatIndex(String period, Double actualTemperature, Double designTemperature,
                                            Integer forecastTarget, Long targetId) {
        // 获取节能建筑和非节能建筑的参数
        LoadParam energyEfficientParam = loadParamMapper.selectByPeriodAndType(period, 1, 1); // 居住+节能
        LoadParam nonEnergyEfficientParam = loadParamMapper.selectByPeriodAndType(period, 1, 2); // 居住+非节能
        
        if (energyEfficientParam == null || nonEnergyEfficientParam == null) {
            throw new RuntimeException("缺少建筑参数配置");
        }

        // 获取面积比例
        List<LoadArea> areas = loadAreaMapper.selectByPeriodTargetAndId(period, forecastTarget, targetId);
        if (areas.isEmpty()) {
            throw new RuntimeException("缺少面积配置");
        }

        // 计算节能建筑和非节能建筑的面积比例
        double totalArea = areas.stream().mapToDouble(LoadArea::getArea).sum();
        double energyEfficientArea = areas.stream()
                .filter(area -> area.getInsulated() == 1)
                .mapToDouble(LoadArea::getArea).sum();
        double nonEnergyEfficientArea = totalArea - energyEfficientArea;

        double m1 = energyEfficientArea / totalArea; // 节能建筑占比
        double m2 = nonEnergyEfficientArea / totalArea; // 非节能建筑占比

        // W1：节能建筑采暖热指标，W2：非节能建筑采暖热指标
        double w1 = energyEfficientParam.getEfficiency();
        double w2 = nonEnergyEfficientParam.getEfficiency();

        // 计算标杆热指标
        double benchmarkIndex = (w1 * m1 + w2 * m2) * (18 - actualTemperature) / (18 - designTemperature);
        
        return benchmarkIndex;
    }

    /**
     * 计算历史修正系数K1
     * 公式：K1 = (∑W实际 / ∑W预测) × (T设计 - T实际) / (T设计 - T基准)
     */
    public Double calculateHistoricalCorrectionK1(String period, Integer forecastTarget, Long targetId,
                                                 Double minTemperature, Double maxTemperature,
                                                 Double designTemperature, Double baseTemperature) {
        // 获取温度区间内的历史数据
        List<LoadDay> historicalData = loadDayMapper.selectHistoricalDataForK1(
                period, forecastTarget, targetId, minTemperature, maxTemperature);

        if (historicalData.isEmpty()) {
            return 1.0; // 默认系数
        }

        // 计算各区间的调整系数
        List<Double> coefficients = new ArrayList<>();
        
        for (LoadDay data : historicalData) {
            if (data.getRealEfficiency() != null && data.getEfficiency() != null && data.getEfficiency() > 0) {
                double ratio = data.getRealEfficiency() / data.getEfficiency();
                double tempAdjustment = (designTemperature - data.getTemperature()) / (designTemperature - baseTemperature);
                double coefficient = ratio * tempAdjustment;
                coefficients.add(coefficient);
            }
        }

        if (coefficients.isEmpty()) {
            return 1.0;
        }

        // 计算标准差
        double mean = coefficients.stream().mapToDouble(Double::doubleValue).average().orElse(1.0);
        double variance = coefficients.stream()
                .mapToDouble(coeff -> Math.pow(coeff - mean, 2))
                .average().orElse(0.0);
        double standardDeviation = Math.sqrt(variance);

        // 如果标准差>0.1，需要拆分区间（这里简化处理，返回均值）
        if (standardDeviation > 0.1) {
            // TODO: 实现区间拆分逻辑
        }

        return mean;
    }

    /**
     * 计算实时修正系数K2
     * 公式：K2 = (1/n) × ∑(W实际(i) / W预测(i))
     */
    public Double calculateRealtimeCorrectionK2(String period, Integer forecastTarget, Long targetId, Date currentDate) {
        // 获取当年数据
        List<LoadDay> currentYearData = loadDayMapper.selectCurrentYearDataForK2(
                period, forecastTarget, targetId, currentDate);

        if (currentYearData.isEmpty()) {
            return 1.0; // 默认系数
        }

        // 计算实际热量与预测热量的比值
        List<Double> ratios = currentYearData.stream()
                .filter(data -> data.getRealHeat() != null && data.getForecastHeat() != null && data.getForecastHeat() > 0)
                .map(data -> data.getRealHeat() / data.getForecastHeat())
                .collect(Collectors.toList());

        if (ratios.isEmpty()) {
            return 1.0;
        }

        // 返回平均值
        return ratios.stream().mapToDouble(Double::doubleValue).average().orElse(1.0);
    }

    /**
     * 计算预测热指标
     * 公式：预测热指标 = 标杆热指标 × K1 × K2
     */
    public Double calculateForecastHeatIndex(Double benchmarkHeatIndex, Double k1, Double k2) {
        return benchmarkHeatIndex * k1 * k2;
    }

    /**
     * 计算热负荷
     * 公式：热负荷 = 对应热指标 × 实际供热面积 × F1 × F2
     */
    public Double calculateHeatLoad(Double heatIndex, Double actualHeatingArea, Double f1, Double f2) {
        return heatIndex * actualHeatingArea * f1 * f2;
    }

    /**
     * 获取天气修正系数F2
     */
    public Double getWeatherCorrectionF2(String weatherCondition) {
        return WeatherCorrection.getCorrectionFactorByWeather(weatherCondition);
    }

    /**
     * 创建温度区间
     */
    public List<TemperatureInterval> createTemperatureIntervals(String period, Double minTemp, Double maxTemp) {
        List<TemperatureInterval> intervals = new ArrayList<>();
        int intervalId = 1;
        
        // 初始划分：每1℃为一个区间
        for (double temp = minTemp; temp < maxTemp; temp += 1.0) {
            TemperatureInterval interval = new TemperatureInterval(
                    intervalId++, temp, temp + 1.0, period);
            intervals.add(interval);
        }
        
        return intervals;
    }

    /**
     * 自适应调整温度区间
     * 当某区间数据量<样本总量5%时，自动合并相邻区间
     */
    public List<TemperatureInterval> adjustTemperatureIntervals(List<TemperatureInterval> intervals, 
                                                               int totalSamples) {
        List<TemperatureInterval> adjustedIntervals = new ArrayList<>();
        
        for (int i = 0; i < intervals.size(); i++) {
            TemperatureInterval current = intervals.get(i);
            
            // 检查是否需要合并
            if (current.hasInsufficientSamples(totalSamples) && i < intervals.size() - 1) {
                TemperatureInterval next = intervals.get(i + 1);
                
                // 合并当前区间和下一个区间
                TemperatureInterval merged = new TemperatureInterval(
                        current.getIntervalId(),
                        current.getMinTemperature(),
                        next.getMaxTemperature(),
                        current.getPeriod()
                );
                merged.setSampleCount(current.getSampleCount() + next.getSampleCount());
                
                adjustedIntervals.add(merged);
                i++; // 跳过下一个区间
            } else {
                adjustedIntervals.add(current);
            }
        }
        
        return adjustedIntervals;
    }

    /**
     * 判断采暖季阶段
     */
    public String determineHeatingStage(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int month = cal.get(Calendar.MONTH) + 1; // Calendar.MONTH 从0开始
        int day = cal.get(Calendar.DAY_OF_MONTH);
        
        if ((month == 12 && day >= 16) || (month == 1) || (month == 2 && day <= 15)) {
            return HeatingPeriod.STAGE_SEVERE_COLD; // 严寒期：12月16日-2月15日
        } else if (month == 2 && day >= 16) {
            return HeatingPeriod.STAGE_LATE_COLD; // 未寒期：2月16日-供暖结束日
        } else {
            return HeatingPeriod.STAGE_EARLY_COLD; // 初寒期：供暖初始日-12月15日
        }
    }

    /**
     * 获取阶段设计温度和基准温度
     */
    public Map<String, Double> getStageTemperatures(String stage) {
        Map<String, Double> temperatures = new HashMap<>();
        
        switch (stage) {
            case HeatingPeriod.STAGE_EARLY_COLD:
                temperatures.put("design", -5.0);
                temperatures.put("base", -3.0);
                break;
            case HeatingPeriod.STAGE_SEVERE_COLD:
                temperatures.put("design", -8.0);
                temperatures.put("base", -6.0);
                break;
            case HeatingPeriod.STAGE_LATE_COLD:
                temperatures.put("design", 0.0);
                temperatures.put("base", 2.0);
                break;
            default:
                temperatures.put("design", -5.0);
                temperatures.put("base", -3.0);
        }
        
        return temperatures;
    }
}
