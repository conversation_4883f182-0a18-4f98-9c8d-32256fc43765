package com.thtf.opengis.loadforecast.entity;

import java.util.Date;
import java.util.List;

/**
 * 温度区间实体类
 * 用于历史数据映射和修正系数计算
 */
public class TemperatureInterval {
    
    /**
     * 区间号
     */
    private Integer intervalId;
    
    /**
     * 温度区间描述，如"-8~-5℃"
     */
    private String temperatureRange;
    
    /**
     * 最低温度
     */
    private Double minTemperature;
    
    /**
     * 最高温度
     */
    private Double maxTemperature;
    
    /**
     * 样本数量
     */
    private Integer sampleCount;
    
    /**
     * 区间系数K1
     */
    private Double intervalCoefficient;
    
    /**
     * 标准差
     */
    private Double standardDeviation;
    
    /**
     * 采暖期
     */
    private String period;
    
    /**
     * 采暖季阶段（初寒期、严寒期、未寒期）
     */
    private String heatingStage;

    // 构造函数
    public TemperatureInterval() {}

    public TemperatureInterval(Integer intervalId, Double minTemperature, Double maxTemperature, String period) {
        this.intervalId = intervalId;
        this.minTemperature = minTemperature;
        this.maxTemperature = maxTemperature;
        this.period = period;
        this.temperatureRange = String.format("%.0f~%.0f℃", minTemperature, maxTemperature);
        this.sampleCount = 0;
    }

    // Getter和Setter方法
    public Integer getIntervalId() {
        return intervalId;
    }

    public void setIntervalId(Integer intervalId) {
        this.intervalId = intervalId;
    }

    public String getTemperatureRange() {
        return temperatureRange;
    }

    public void setTemperatureRange(String temperatureRange) {
        this.temperatureRange = temperatureRange;
    }

    public Double getMinTemperature() {
        return minTemperature;
    }

    public void setMinTemperature(Double minTemperature) {
        this.minTemperature = minTemperature;
        updateTemperatureRange();
    }

    public Double getMaxTemperature() {
        return maxTemperature;
    }

    public void setMaxTemperature(Double maxTemperature) {
        this.maxTemperature = maxTemperature;
        updateTemperatureRange();
    }

    public Integer getSampleCount() {
        return sampleCount;
    }

    public void setSampleCount(Integer sampleCount) {
        this.sampleCount = sampleCount;
    }

    public Double getIntervalCoefficient() {
        return intervalCoefficient;
    }

    public void setIntervalCoefficient(Double intervalCoefficient) {
        this.intervalCoefficient = intervalCoefficient;
    }

    public Double getStandardDeviation() {
        return standardDeviation;
    }

    public void setStandardDeviation(Double standardDeviation) {
        this.standardDeviation = standardDeviation;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getHeatingStage() {
        return heatingStage;
    }

    public void setHeatingStage(String heatingStage) {
        this.heatingStage = heatingStage;
    }

    /**
     * 更新温度区间描述
     */
    private void updateTemperatureRange() {
        if (minTemperature != null && maxTemperature != null) {
            this.temperatureRange = String.format("%.0f~%.0f℃", minTemperature, maxTemperature);
        }
    }

    /**
     * 判断温度是否在此区间内
     */
    public boolean containsTemperature(Double temperature) {
        if (temperature == null || minTemperature == null || maxTemperature == null) {
            return false;
        }
        return temperature >= minTemperature && temperature <= maxTemperature;
    }

    /**
     * 获取区间中心温度
     */
    public Double getCenterTemperature() {
        if (minTemperature == null || maxTemperature == null) {
            return null;
        }
        return (minTemperature + maxTemperature) / 2.0;
    }

    /**
     * 获取区间宽度
     */
    public Double getIntervalWidth() {
        if (minTemperature == null || maxTemperature == null) {
            return null;
        }
        return maxTemperature - minTemperature;
    }

    /**
     * 判断是否需要拆分区间（标准差>0.1）
     */
    public boolean needsSplit() {
        return standardDeviation != null && standardDeviation > 0.1;
    }

    /**
     * 判断样本数量是否过少（<总样本5%）
     */
    public boolean hasInsufficientSamples(Integer totalSamples) {
        if (sampleCount == null || totalSamples == null || totalSamples == 0) {
            return true;
        }
        return (double) sampleCount / totalSamples < 0.05;
    }

    @Override
    public String toString() {
        return "TemperatureInterval{" +
                "intervalId=" + intervalId +
                ", temperatureRange='" + temperatureRange + '\'' +
                ", minTemperature=" + minTemperature +
                ", maxTemperature=" + maxTemperature +
                ", sampleCount=" + sampleCount +
                ", intervalCoefficient=" + intervalCoefficient +
                ", standardDeviation=" + standardDeviation +
                ", period='" + period + '\'' +
                ", heatingStage='" + heatingStage + '\'' +
                '}';
    }
}
