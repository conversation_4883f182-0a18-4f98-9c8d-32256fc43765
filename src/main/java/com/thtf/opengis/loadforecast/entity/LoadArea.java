package com.thtf.opengis.loadforecast.entity;

import java.util.Date;

/**
 * 预测面积实体类
 * 对应表：op_loadarea_t
 */
public class LoadArea {
    
    private Long id;
    
    /**
     * 采暖期
     */
    private String period;
    
    /**
     * 预测对象(1热网2热源3换热站)
     */
    private Integer forecastTarget;
    
    /**
     * 对象id
     */
    private Long targetId;
    
    /**
     * 面积日期
     */
    private Date areaDate;
    
    /**
     * 建筑性质(1居住2办公3营业)
     */
    private Integer buildingType;
    
    /**
     * 节能性(1有保温2无保温)
     */
    private Integer insulated;
    
    /**
     * 正常面积
     */
    private Double areaNormal;
    
    /**
     * 停暖面积
     */
    private Double areaStop;
    
    /**
     * 停供面积比例
     */
    private Double stopProportion;
    
    /**
     * 调节面积
     */
    private Double areaAdjust;
    
    /**
     * 面积
     */
    private Double area;

    // 构造函数
    public LoadArea() {}

    public LoadArea(String period, Integer forecastTarget, Long targetId, Date areaDate, 
                   Integer buildingType, Integer insulated, Double area) {
        this.period = period;
        this.forecastTarget = forecastTarget;
        this.targetId = targetId;
        this.areaDate = areaDate;
        this.buildingType = buildingType;
        this.insulated = insulated;
        this.area = area;
        this.areaNormal = area;
        this.areaStop = 0.0;
        this.stopProportion = 0.0;
        this.areaAdjust = 0.0;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Integer getForecastTarget() {
        return forecastTarget;
    }

    public void setForecastTarget(Integer forecastTarget) {
        this.forecastTarget = forecastTarget;
    }

    public Long getTargetId() {
        return targetId;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public Date getAreaDate() {
        return areaDate;
    }

    public void setAreaDate(Date areaDate) {
        this.areaDate = areaDate;
    }

    public Integer getBuildingType() {
        return buildingType;
    }

    public void setBuildingType(Integer buildingType) {
        this.buildingType = buildingType;
    }

    public Integer getInsulated() {
        return insulated;
    }

    public void setInsulated(Integer insulated) {
        this.insulated = insulated;
    }

    public Double getAreaNormal() {
        return areaNormal;
    }

    public void setAreaNormal(Double areaNormal) {
        this.areaNormal = areaNormal;
    }

    public Double getAreaStop() {
        return areaStop;
    }

    public void setAreaStop(Double areaStop) {
        this.areaStop = areaStop;
    }

    public Double getStopProportion() {
        return stopProportion;
    }

    public void setStopProportion(Double stopProportion) {
        this.stopProportion = stopProportion;
    }

    public Double getAreaAdjust() {
        return areaAdjust;
    }

    public void setAreaAdjust(Double areaAdjust) {
        this.areaAdjust = areaAdjust;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    /**
     * 获取预测对象描述
     */
    public String getForecastTargetDesc() {
        switch (forecastTarget) {
            case 1: return "热网";
            case 2: return "热源";
            case 3: return "换热站";
            default: return "未知";
        }
    }

    /**
     * 获取建筑类型描述
     */
    public String getBuildingTypeDesc() {
        switch (buildingType) {
            case 1: return "居住";
            case 2: return "办公";
            case 3: return "营业";
            default: return "未知";
        }
    }

    /**
     * 获取节能性描述
     */
    public String getInsulatedDesc() {
        switch (insulated) {
            case 1: return "有保温";
            case 2: return "无保温";
            default: return "未知";
        }
    }

    /**
     * 计算实际供热面积
     */
    public Double getActualHeatingArea() {
        return area - areaStop + areaAdjust;
    }

    @Override
    public String toString() {
        return "LoadArea{" +
                "id=" + id +
                ", period='" + period + '\'' +
                ", forecastTarget=" + forecastTarget +
                ", targetId=" + targetId +
                ", areaDate=" + areaDate +
                ", buildingType=" + buildingType +
                ", insulated=" + insulated +
                ", areaNormal=" + areaNormal +
                ", areaStop=" + areaStop +
                ", stopProportion=" + stopProportion +
                ", areaAdjust=" + areaAdjust +
                ", area=" + area +
                '}';
    }
}
