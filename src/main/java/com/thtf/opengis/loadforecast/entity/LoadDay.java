package com.thtf.opengis.loadforecast.entity;

import java.util.Date;

/**
 * 负荷预测天实体类
 * 对应表：op_loadday_t
 */
public class LoadDay {
    
    private Long id;
    
    /**
     * 采暖期
     */
    private String period;
    
    /**
     * 预测对象(1热网2热源3换热站)
     */
    private Integer forecastTarget;
    
    /**
     * 对象名称
     */
    private String targetName;
    
    /**
     * 预测日期
     */
    private Date forecastDate;
    
    /**
     * 面积
     */
    private Double area;
    
    /**
     * 外温
     */
    private Double temperature;
    
    /**
     * 预测热量
     */
    private Double forecastHeat;
    
    /**
     * 实际热量
     */
    private Double realHeat;
    
    /**
     * 能耗(w/m)
     */
    private Double efficiency;
    
    /**
     * 对象id
     */
    private Long targetId;
    
    /**
     * 实际能耗
     */
    private Double realEfficiency;

    // 构造函数
    public LoadDay() {}

    public LoadDay(String period, Integer forecastTarget, String targetName, Date forecastDate, 
                  Double area, Double temperature, Long targetId) {
        this.period = period;
        this.forecastTarget = forecastTarget;
        this.targetName = targetName;
        this.forecastDate = forecastDate;
        this.area = area;
        this.temperature = temperature;
        this.targetId = targetId;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Integer getForecastTarget() {
        return forecastTarget;
    }

    public void setForecastTarget(Integer forecastTarget) {
        this.forecastTarget = forecastTarget;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public Date getForecastDate() {
        return forecastDate;
    }

    public void setForecastDate(Date forecastDate) {
        this.forecastDate = forecastDate;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public Double getForecastHeat() {
        return forecastHeat;
    }

    public void setForecastHeat(Double forecastHeat) {
        this.forecastHeat = forecastHeat;
    }

    public Double getRealHeat() {
        return realHeat;
    }

    public void setRealHeat(Double realHeat) {
        this.realHeat = realHeat;
    }

    public Double getEfficiency() {
        return efficiency;
    }

    public void setEfficiency(Double efficiency) {
        this.efficiency = efficiency;
    }

    public Long getTargetId() {
        return targetId;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public Double getRealEfficiency() {
        return realEfficiency;
    }

    public void setRealEfficiency(Double realEfficiency) {
        this.realEfficiency = realEfficiency;
    }

    /**
     * 获取预测对象描述
     */
    public String getForecastTargetDesc() {
        switch (forecastTarget) {
            case 1: return "热网";
            case 2: return "热源";
            case 3: return "换热站";
            default: return "未知";
        }
    }

    /**
     * 计算预测准确率
     */
    public Double getAccuracy() {
        if (realHeat == null || forecastHeat == null || realHeat == 0) {
            return null;
        }
        return 1.0 - Math.abs(forecastHeat - realHeat) / realHeat;
    }

    /**
     * 计算热指标偏差
     */
    public Double getEfficiencyDeviation() {
        if (realEfficiency == null || efficiency == null) {
            return null;
        }
        return realEfficiency - efficiency;
    }

    @Override
    public String toString() {
        return "LoadDay{" +
                "id=" + id +
                ", period='" + period + '\'' +
                ", forecastTarget=" + forecastTarget +
                ", targetName='" + targetName + '\'' +
                ", forecastDate=" + forecastDate +
                ", area=" + area +
                ", temperature=" + temperature +
                ", forecastHeat=" + forecastHeat +
                ", realHeat=" + realHeat +
                ", efficiency=" + efficiency +
                ", targetId=" + targetId +
                ", realEfficiency=" + realEfficiency +
                '}';
    }
}
