package com.thtf.opengis.loadforecast.entity;

/**
 * 预测参数实体类
 * 对应表：op_loadparam_t
 */
public class LoadParam {
    
    private Long id;
    
    /**
     * 采暖期
     */
    private String period;
    
    /**
     * 建筑性质(1居住2办公3营业)
     */
    private Integer buildingType;
    
    /**
     * 节能性(1有保温2无保温)
     */
    private Integer insulated;
    
    /**
     * 能耗(w/m)
     */
    private Double efficiency;

    // 构造函数
    public LoadParam() {}

    public LoadParam(String period, Integer buildingType, Integer insulated, Double efficiency) {
        this.period = period;
        this.buildingType = buildingType;
        this.insulated = insulated;
        this.efficiency = efficiency;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Integer getBuildingType() {
        return buildingType;
    }

    public void setBuildingType(Integer buildingType) {
        this.buildingType = buildingType;
    }

    public Integer getInsulated() {
        return insulated;
    }

    public void setInsulated(Integer insulated) {
        this.insulated = insulated;
    }

    public Double getEfficiency() {
        return efficiency;
    }

    public void setEfficiency(Double efficiency) {
        this.efficiency = efficiency;
    }

    /**
     * 获取建筑类型描述
     */
    public String getBuildingTypeDesc() {
        switch (buildingType) {
            case 1: return "居住";
            case 2: return "办公";
            case 3: return "营业";
            default: return "未知";
        }
    }

    /**
     * 获取节能性描述
     */
    public String getInsulatedDesc() {
        switch (insulated) {
            case 1: return "有保温";
            case 2: return "无保温";
            default: return "未知";
        }
    }

    /**
     * 判断是否为节能建筑
     */
    public boolean isEnergyEfficient() {
        return insulated != null && insulated == 1;
    }

    @Override
    public String toString() {
        return "LoadParam{" +
                "id=" + id +
                ", period='" + period + '\'' +
                ", buildingType=" + buildingType +
                ", insulated=" + insulated +
                ", efficiency=" + efficiency +
                '}';
    }
}
