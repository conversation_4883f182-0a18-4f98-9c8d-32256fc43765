package com.thtf.opengis.loadforecast.entity;

import java.util.Date;

/**
 * 负荷百分比实体类
 * 对应表：op_loadproportion_t
 */
public class LoadProportion {
    
    private Integer id;
    
    /**
     * 采暖期
     */
    private String period;
    
    /**
     * 外温
     */
    private Double temperature;
    
    /**
     * 比例
     */
    private Double proportion;
    
    /**
     * 能耗(w/m)
     */
    private Double efficiency;
    
    /**
     * 创建人
     */
    private Integer creator;
    
    /**
     * 创建人名称
     */
    private String creatorName;
    
    /**
     * 创建日期
     */
    private Date createDate;
    
    /**
     * 修改人
     */
    private Integer updator;
    
    /**
     * 修改人名称
     */
    private String updatorName;
    
    /**
     * 修改日期
     */
    private Date updateDate;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 有效(0无效1有效)
     */
    private Integer valid;

    // 构造函数
    public LoadProportion() {}

    public LoadProportion(String period, Double temperature, Double proportion, Double efficiency) {
        this.period = period;
        this.temperature = temperature;
        this.proportion = proportion;
        this.efficiency = efficiency;
        this.valid = 1;
        this.createDate = new Date();
    }

    // Getter和Setter方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public Double getProportion() {
        return proportion;
    }

    public void setProportion(Double proportion) {
        this.proportion = proportion;
    }

    public Double getEfficiency() {
        return efficiency;
    }

    public void setEfficiency(Double efficiency) {
        this.efficiency = efficiency;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    @Override
    public String toString() {
        return "LoadProportion{" +
                "id=" + id +
                ", period='" + period + '\'' +
                ", temperature=" + temperature +
                ", proportion=" + proportion +
                ", efficiency=" + efficiency +
                ", creator=" + creator +
                ", creatorName='" + creatorName + '\'' +
                ", createDate=" + createDate +
                ", updator=" + updator +
                ", updatorName='" + updatorName + '\'' +
                ", updateDate=" + updateDate +
                ", remark='" + remark + '\'' +
                ", valid=" + valid +
                '}';
    }
}
